{"coverUrls": [["https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/no_rush-tokyo_tea_room", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/hurry_up_tomorrow-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/demo-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/everythingisworseatnight-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_boy_is_mine-a<PERSON>a_grande", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/clancy-twenty_one_pilots", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/we_don_t_trust_you-future", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/hit_me_hard_and_soft-billie_eilish", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/radical_optimism-dua_lipa", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/moon_music-coldplay", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/eternal_sunshine-ariana_grande", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/mixtape_pluto-future", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/gnx-kend<PERSON>_lamar", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/softwareupdate2.0-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/cadaver-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/zip-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/champion-bones", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/the_tortured_poets_department_the_anthology-taylor_swift", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/for_all_the_dogs-drake", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/one_thing_at_a_time-morgan_wallen", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/espresso-sabrina_carpenter", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/guts-olivia_rod<PERSON>o", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/starboy_deluxe_-the_weeknd", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/1989_taylor_s_version_-taylor_swift", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/la_vida_es_una-myke_towers", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/coco_moon-owl_city", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/dead_club_city_extended_deluxe_-nothing_but_thieves", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/sos_deluxe_-sza", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/love_sick_deluxe_-don_toliver", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/songs_of_surrender-u2", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/speak_now_taylor_s_version_-taylor_swift", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/dead_club_city-nothing_but_thieves", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/spider-man_across_the_spider-verse-metro_boomin", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/manana_sera_bonito-karol_g", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/5-ed_sheeran", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/unreal_unearth-hozier", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/austin-post_malone", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/did_you_know_that_there_s_a_tunnel_under_ocean_blvd-lana_del_rey", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/utopia-travis_scott", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/autumn_variations-ed_sheeran", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/love_sick-don_toliver", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/amar-bigxthaplug", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/cuts_bruises-inhaler", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/a_dream_about_love-circa_survive", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/fantasy-m83", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/nadie_sabe_lo_que_va_a_pasar_manana-bad_bunny", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/eternal_embers-meltt", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/coping_habits-beach_vacation", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/exit_signs-leap", "https://zapqcxbffugqvfiiilci.supabase.co/storage/v1/object/public/albumcovers/images/thewitch_thewizard-bones"]], "musicData": [{"title": "Blinding Lights", "labelName": "Starlight Records", "albumName": "Neon Dreams", "trackInfo": "An electrifying synthwave anthem about love and longing in the city lights", "primaryLanguage": "en", "upc": "12345678001", "isrc": "USRC240001", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.electronic", "copyrightName": "Starlight Records", "copyrightYear": 2024, "phonogramCopyright": "Starlight Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track001.mp3"]}, {"title": "Watermelon Sugar", "labelName": "Summer Vibes Records", "albumName": "Sweet Memories", "trackInfo": "A feel-good summer anthem celebrating life's simple pleasures and sweet moments", "primaryLanguage": "en", "upc": "12345678002", "isrc": "USRC240002", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rock", "copyrightName": "Summer Vibes Records", "copyrightYear": 2024, "phonogramCopyright": "Summer Vibes Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track002.mp3"]}, {"title": "Levitating", "labelName": "Future Pop Records", "albumName": "Disco Fever", "trackInfo": "An infectious disco-pop track that makes you want to dance all night long", "primaryLanguage": "en", "upc": "12345678003", "isrc": "USRC240003", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Future Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Future Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track003.mp3"]}, {"title": "Good 4 U", "labelName": "Indie Rock Records", "albumName": "Emotional Rollercoaster", "trackInfo": "A powerful pop-punk anthem about moving on and finding strength after heartbreak", "primaryLanguage": "en", "upc": "12345678004", "isrc": "USRC240004", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.pop", "copyrightName": "Indie Rock Records", "copyrightYear": 2024, "phonogramCopyright": "Indie Rock Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track004.mp3"]}, {"title": "Stay", "labelName": "Heartfelt Music", "albumName": "Vulnerable Moments", "trackInfo": "An emotional ballad about the fear of losing someone you love and wanting them to stay", "primaryLanguage": "en", "upc": "12345678005", "isrc": "USRC240005", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.ballad", "copyrightName": "Heartfelt Music", "copyrightYear": 2024, "phonogramCopyright": "Heartfelt Music", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track005.mp3"]}, {"title": "Anti-Hero", "labelName": "Midnight Records", "albumName": "Self Reflection", "trackInfo": "A introspective pop song about self-doubt and the complexities of being human", "primaryLanguage": "en", "upc": "12345678006", "isrc": "USRC240006", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Midnight Records", "copyrightYear": 2024, "phonogramCopyright": "Midnight Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track006.mp3"]}, {"title": "As It Was", "labelName": "Nostalgic Sounds", "albumName": "Time Capsule", "trackInfo": "A melancholic reflection on change and the passage of time with dreamy melodies", "primaryLanguage": "en", "upc": "12345678007", "isrc": "USRC240007", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.indie", "copyrightName": "Nostalgic Sounds", "copyrightYear": 2024, "phonogramCopyright": "Nostalgic Sounds", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track007.mp3"]}, {"title": "Heat Waves", "labelName": "Indie Wave Records", "albumName": "Summer Haze", "trackInfo": "A dreamy indie-pop track about longing and the hazy memories of summer love", "primaryLanguage": "en", "upc": "12345678008", "isrc": "USRC240008", "primaryGenreId": "music.genre.indie", "secondaryGenreId": "music.genre.pop", "copyrightName": "Indie Wave Records", "copyrightYear": 2024, "phonogramCopyright": "Indie Wave Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track008.mp3"]}, {"title": "Bad <PERSON>bit", "labelName": "Smooth Groove Records", "albumName": "Midnight Confessions", "trackInfo": "A smooth R&B-influenced track about irresistible attraction and late-night desires", "primaryLanguage": "en", "upc": "12345678009", "isrc": "USRC240009", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.pop", "copyrightName": "Smooth Groove Records", "copyrightYear": 2024, "phonogramCopyright": "Smooth Groove Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track009.mp3"]}, {"title": "About Damn Time", "labelName": "Empowerment Records", "albumName": "Self Love Anthem", "trackInfo": "An uplifting anthem about self-confidence and celebrating your own worth", "primaryLanguage": "en", "upc": "12345678010", "isrc": "USRC240010", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.funk", "copyrightName": "Empowerment Records", "copyrightYear": 2024, "phonogramCopyright": "Empowerment Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track010.mp3"]}, {"title": "Running Up That Hill", "labelName": "Classic Revival Records", "albumName": "Timeless Echoes", "trackInfo": "A haunting synth-pop masterpiece about empathy and understanding between lovers", "primaryLanguage": "en", "upc": "12345678011", "isrc": "USRC240011", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.synthpop", "copyrightName": "Classic Revival Records", "copyrightYear": 2024, "phonogramCopyright": "Classic Revival Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track011.mp3"]}, {"title": "Flowers", "labelName": "Self Love Records", "albumName": "Independence Day", "trackInfo": "An empowering pop anthem about self-love and finding strength after a breakup", "primaryLanguage": "en", "upc": "12345678012", "isrc": "USRC240012", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Self Love Records", "copyrightYear": 2024, "phonogramCopyright": "Self Love Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track012.mp3"]}, {"title": "Unholy", "labelName": "Dark Pop Records", "albumName": "Forbidden Desires", "trackInfo": "A seductive dark pop track exploring themes of temptation and forbidden love", "primaryLanguage": "en", "upc": "12345678013", "isrc": "USRC240013", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dark", "copyrightName": "Dark Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Dark Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track013.mp3"]}, {"title": "I'm <PERSON> (Blue)", "labelName": "Electronic Beats", "albumName": "Dance Floor Anthems", "trackInfo": "An energetic dance track that combines classic house vibes with modern production", "primaryLanguage": "en", "upc": "12345678014", "isrc": "USRC240014", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.dance", "copyrightName": "Electronic Beats", "copyrightYear": 2024, "phonogramCopyright": "Electronic Beats", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track014.mp3"]}, {"title": "Calm Down", "labelName": "Afrobeats Global", "albumName": "African Rhythms", "trackInfo": "A smooth Afrobeats track with infectious rhythms and romantic lyrics", "primaryLanguage": "en", "upc": "12345678015", "isrc": "USRC240015", "primaryGenreId": "music.genre.afrobeats", "secondaryGenreId": "music.genre.pop", "copyrightName": "Afrobeats Global", "copyrightYear": 2024, "phonogramCopyright": "Afrobeats Global", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track015.mp3"]}, {"title": "Shivers", "labelName": "Acoustic Soul Records", "albumName": "Intimate Moments", "trackInfo": "A tender acoustic ballad about the physical and emotional effects of deep love", "primaryLanguage": "en", "upc": "12345678016", "isrc": "USRC240016", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.acoustic", "copyrightName": "Acoustic Soul Records", "copyrightYear": 2024, "phonogramCopyright": "Acoustic Soul Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track016.mp3"]}, {"title": "Industry Baby", "labelName": "Hip Hop Empire", "albumName": "Chart Toppers", "trackInfo": "A bold hip-hop anthem celebrating success and breaking barriers in the music industry", "primaryLanguage": "en", "upc": "12345678017", "isrc": "USRC240017", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Hip Hop Empire", "copyrightYear": 2024, "phonogramCopyright": "Hip Hop Empire", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track017.mp3"]}, {"title": "Ghost", "labelName": "Indie Folk Records", "albumName": "Haunted Melodies", "trackInfo": "A haunting folk ballad about lost love and the memories that linger like ghosts", "primaryLanguage": "en", "upc": "12345678018", "isrc": "USRC240018", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.indie", "copyrightName": "Indie Folk Records", "copyrightYear": 2024, "phonogramCopyright": "Indie Folk Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track018.mp3"]}, {"title": "Peaches", "labelName": "R&B Smooth", "albumName": "Velvet Nights", "trackInfo": "A smooth R&B track with sensual lyrics and silky vocals about desire and intimacy", "primaryLanguage": "en", "upc": "12345678019", "isrc": "USRC240019", "primaryGenreId": "music.genre.rnb", "secondaryGenreId": "music.genre.pop", "copyrightName": "R&B Smooth", "copyrightYear": 2024, "phonogramCopyright": "R&B Smooth", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track019.mp3"]}, {"title": "<PERSON><PERSON> (Call Me By Your Name)", "labelName": "Bold Expression Records", "albumName": "Authentic Self", "trackInfo": "A provocative pop-rap anthem about self-acceptance and living authentically", "primaryLanguage": "en", "upc": "12345678020", "isrc": "USRC240020", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rap", "copyrightName": "Bold Expression Records", "copyrightYear": 2024, "phonogramCopyright": "Bold Expression Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track020.mp3"]}, {"title": "Drivers License", "labelName": "Teenage Dreams Records", "albumName": "Coming of Age", "trackInfo": "A heartbreaking ballad about first love, heartbreak, and growing up too fast", "primaryLanguage": "en", "upc": "12345678021", "isrc": "USRC240021", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.ballad", "copyrightName": "Teenage Dreams Records", "copyrightYear": 2024, "phonogramCopyright": "Teenage Dreams Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track021.mp3"]}, {"title": "Positions", "labelName": "Sultry Sounds", "albumName": "Intimate Confessions", "trackInfo": "A sultry R&B-pop track about devotion and the many ways to show love", "primaryLanguage": "en", "upc": "12345678022", "isrc": "USRC240022", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Sultry Sounds", "copyrightYear": 2024, "phonogramCopyright": "Sultry Sounds", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track022.mp3"]}, {"title": "Willow", "labelName": "Folk Pop Records", "albumName": "Nature's Symphony", "trackInfo": "A dreamy folk-pop song using nature metaphors to describe the flow of love", "primaryLanguage": "en", "upc": "12345678023", "isrc": "USRC240023", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.pop", "copyrightName": "Folk Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Folk Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track023.mp3"]}, {"title": "<PERSON>igan", "labelName": "Storyteller Records", "albumName": "Folklore Tales", "trackInfo": "A nostalgic indie-folk ballad about young love and the comfort of familiar memories", "primaryLanguage": "en", "upc": "12345678024", "isrc": "USRC240024", "primaryGenreId": "music.genre.folk", "secondaryGenreId": "music.genre.indie", "copyrightName": "Storyteller Records", "copyrightYear": 2024, "phonogramCopyright": "Storyteller Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track024.mp3"]}, {"title": "Circles", "labelName": "Melancholy Music", "albumName": "Endless Loops", "trackInfo": "A melancholic hip-hop ballad about being stuck in toxic relationship patterns", "primaryLanguage": "en", "upc": "12345678025", "isrc": "USRC240025", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Melancholy Music", "copyrightYear": 2024, "phonogramCopyright": "Melancholy Music", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track025.mp3"]}, {"title": "<PERSON>", "labelName": "Confidence Records", "albumName": "Boss Energy", "trackInfo": "A fierce hip-hop anthem celebrating confidence, independence, and female empowerment", "primaryLanguage": "en", "upc": "12345678026", "isrc": "USRC240026", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.pop", "copyrightName": "Confidence Records", "copyrightYear": 2024, "phonogramCopyright": "Confidence Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track026.mp3"]}, {"title": "Dynamite", "labelName": "K-Pop Global", "albumName": "International Vibes", "trackInfo": "An upbeat disco-pop track that brings joy and energy with its retro-inspired sound", "primaryLanguage": "en", "upc": "12345678027", "isrc": "USRC240027", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.disco", "copyrightName": "K-Pop Global", "copyrightYear": 2024, "phonogramCopyright": "K-Pop Global", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track027.mp3"]}, {"title": "<PERSON><PERSON>", "labelName": "Chill Vibes Records", "albumName": "Laid Back Anthems", "trackInfo": "A laid-back hip-hop track about enjoying life's simple pleasures and good vibes", "primaryLanguage": "en", "upc": "12345678028", "isrc": "USRC240028", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.chill", "copyrightName": "Chill Vibes Records", "copyrightYear": 2024, "phonogramCopyright": "Chill Vibes Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track028.mp3"]}, {"title": "Rockstar", "labelName": "Trap Nation Records", "albumName": "Street Anthems", "trackInfo": "A hard-hitting trap anthem about success, fame, and the rockstar lifestyle", "primaryLanguage": "en", "upc": "12345678029", "isrc": "USRC240029", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.trap", "copyrightName": "Trap Nation Records", "copyrightYear": 2024, "phonogramCopyright": "Trap Nation Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track029.mp3"]}, {"title": "Adore You", "labelName": "Love Songs Records", "albumName": "Romantic Gestures", "trackInfo": "A sweet pop ballad about unconditional love and adoration for someone special", "primaryLanguage": "en", "upc": "12345678030", "isrc": "USRC240030", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.ballad", "copyrightName": "Love Songs Records", "copyrightYear": 2024, "phonogramCopyright": "Love Songs Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track030.mp3"]}, {"title": "Intentions", "labelName": "Pure Love Records", "albumName": "Honest Hearts", "trackInfo": "A heartfelt R&B-pop track about genuine love and pure romantic intentions", "primaryLanguage": "en", "upc": "12345678031", "isrc": "USRC240031", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Pure Love Records", "copyrightYear": 2024, "phonogramCopyright": "Pure Love Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track031.mp3"]}, {"title": "The Box", "labelName": "Trap House Records", "albumName": "Street Stories", "trackInfo": "A minimalist trap hit with a catchy hook and raw street storytelling", "primaryLanguage": "en", "upc": "12345678032", "isrc": "USRC240032", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.trap", "copyrightName": "Trap House Records", "copyrightYear": 2024, "phonogramCopyright": "Trap House Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track032.mp3"]}, {"title": "Memories", "labelName": "Nostalgic Pop Records", "albumName": "Time Machine", "trackInfo": "An emotional pop-rock anthem about cherishing memories and honoring lost loved ones", "primaryLanguage": "en", "upc": "12345678033", "isrc": "USRC240033", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rock", "copyrightName": "Nostalgic Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Nostalgic Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track033.mp3"]}, {"title": "Someone You Loved", "labelName": "Heartbreak Records", "albumName": "Emotional Journey", "trackInfo": "A powerful piano ballad about the pain of losing someone you deeply loved", "primaryLanguage": "en", "upc": "12345678034", "isrc": "USRC240034", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.ballad", "copyrightName": "Heartbreak Records", "copyrightYear": 2024, "phonogramCopyright": "Heartbreak Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track034.mp3"]}, {"title": "Sunflower", "labelName": "Feel Good Records", "albumName": "Positive Vibes", "trackInfo": "An uplifting hip-hop track with a sunny disposition and feel-good energy", "primaryLanguage": "en", "upc": "12345678035", "isrc": "USRC240035", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.pop", "copyrightName": "Feel Good Records", "copyrightYear": 2024, "phonogramCopyright": "Feel Good Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track035.mp3"]}, {"title": "Bad Guy", "labelName": "Alternative Pop Records", "albumName": "Dark Pop Anthems", "trackInfo": "A minimalist dark pop track with a haunting beat and provocative lyrics", "primaryLanguage": "en", "upc": "12345678036", "isrc": "USRC240036", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.alternative", "copyrightName": "Alternative Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Alternative Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track036.mp3"]}, {"title": "7 rings", "labelName": "Luxury Lifestyle Records", "albumName": "Material Dreams", "trackInfo": "A trap-influenced pop anthem about success, luxury, and treating yourself", "primaryLanguage": "en", "upc": "12345678037", "isrc": "USRC240037", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.trap", "copyrightName": "Luxury Lifestyle Records", "copyrightYear": 2024, "phonogramCopyright": "Luxury Lifestyle Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track037.mp3"]}, {"title": "Old Town Road", "labelName": "Country Trap Records", "albumName": "<PERSON><PERSON>", "trackInfo": "A genre-defying country-trap fusion about freedom and the open road", "primaryLanguage": "en", "upc": "12345678038", "isrc": "USRC240038", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.hiphop", "copyrightName": "Country Trap Records", "copyrightYear": 2024, "phonogramCopyright": "Country Trap Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track038.mp3"]}, {"title": "Truth Hurts", "labelName": "Self Love Anthems", "albumName": "Confidence Boost", "trackInfo": "An empowering anthem about self-worth and moving on from toxic relationships", "primaryLanguage": "en", "upc": "12345678039", "isrc": "USRC240039", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.rnb", "copyrightName": "Self Love Anthems", "copyrightYear": 2024, "phonogramCopyright": "Self Love Anthems", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track039.mp3"]}, {"title": "Senorita", "labelName": "Latin Pop Records", "albumName": "Tropical Romance", "trackInfo": "A sultry Latin-influenced pop duet about passionate romance and desire", "primaryLanguage": "en", "upc": "12345678040", "isrc": "USRC240040", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.latin", "copyrightName": "Latin Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Latin Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track040.mp3"]}, {"title": "Lose You To Love Me", "labelName": "Healing Hearts Records", "albumName": "Self Discovery", "trackInfo": "An emotional ballad about finding yourself after letting go of a toxic relationship", "primaryLanguage": "en", "upc": "12345678041", "isrc": "USRC240041", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.ballad", "copyrightName": "Healing Hearts Records", "copyrightYear": 2024, "phonogramCopyright": "Healing Hearts Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track041.mp3"]}, {"title": "Circles", "labelName": "Indie Rock Revival", "albumName": "Modern Classics", "trackInfo": "A dreamy indie-rock track about being caught in endless cycles of love and loss", "primaryLanguage": "en", "upc": "12345678042", "isrc": "USRC240042", "primaryGenreId": "music.genre.rock", "secondaryGenreId": "music.genre.indie", "copyrightName": "Indie Rock Revival", "copyrightYear": 2024, "phonogramCopyright": "Indie Rock Revival", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track042.mp3"]}, {"title": "Falling", "labelName": "Vulnerable Voices", "albumName": "Raw Emotions", "trackInfo": "A stripped-down acoustic ballad about vulnerability and the fear of falling in love", "primaryLanguage": "en", "upc": "12345678043", "isrc": "USRC240043", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.acoustic", "copyrightName": "Vulnerable Voices", "copyrightYear": 2024, "phonogramCopyright": "Vulnerable Voices", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track043.mp3"]}, {"title": "Roses", "labelName": "Electronic Fusion", "albumName": "Digital Dreams", "trackInfo": "An electronic-pop fusion track with infectious beats and romantic undertones", "primaryLanguage": "en", "upc": "12345678044", "isrc": "USRC240044", "primaryGenreId": "music.genre.electronic", "secondaryGenreId": "music.genre.pop", "copyrightName": "Electronic Fusion", "copyrightYear": 2024, "phonogramCopyright": "Electronic Fusion", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track044.mp3"]}, {"title": "Blueberry Faygo", "labelName": "Melodic Rap Records", "albumName": "Sweet Sounds", "trackInfo": "A melodic rap track with catchy hooks and smooth production about success and lifestyle", "primaryLanguage": "en", "upc": "12345678045", "isrc": "USRC240045", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.melodic", "copyrightName": "Melodic Rap Records", "copyrightYear": 2024, "phonogramCopyright": "Melodic Rap Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track045.mp3"]}, {"title": "<PERSON><PERSON>", "labelName": "Dance Craze Records", "albumName": "Viral Moves", "trackInfo": "A catchy hip-hop track designed for dancing with simple, memorable choreography", "primaryLanguage": "en", "upc": "12345678046", "isrc": "USRC240046", "primaryGenreId": "music.genre.hiphop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Dance Craze Records", "copyrightYear": 2024, "phonogramCopyright": "Dance Craze Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track046.mp3"]}, {"title": "Say So", "labelName": "TikTok Hits Records", "albumName": "Social Media Anthems", "trackInfo": "A disco-influenced pop track that became a viral sensation with its catchy dance moves", "primaryLanguage": "en", "upc": "12345678047", "isrc": "USRC240047", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.disco", "copyrightName": "TikTok Hits Records", "copyrightYear": 2024, "phonogramCopyright": "TikTok Hits Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track047.mp3"]}, {"title": "The Bones", "labelName": "Country Soul Records", "albumName": "Strong Foundations", "trackInfo": "A country-pop ballad about having a strong foundation in love and relationships", "primaryLanguage": "en", "upc": "12345678048", "isrc": "USRC240048", "primaryGenreId": "music.genre.country", "secondaryGenreId": "music.genre.pop", "copyrightName": "Country Soul Records", "copyrightYear": 2024, "phonogramCopyright": "Country Soul Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track048.mp3"]}, {"title": "Stupid Love", "labelName": "Dance Pop Records", "albumName": "Love Revolution", "trackInfo": "An energetic dance-pop anthem about the irresistible power of love and attraction", "primaryLanguage": "en", "upc": "12345678049", "isrc": "USRC240049", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Dance Pop Records", "copyrightYear": 2024, "phonogramCopyright": "Dance Pop Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track049.mp3"]}, {"title": "Rain on Me", "labelName": "Collaboration Records", "albumName": "Dance Floor Hits", "trackInfo": "An uplifting dance-pop collaboration about resilience and dancing through life's storms", "primaryLanguage": "en", "upc": "12345678050", "isrc": "USRC240050", "primaryGenreId": "music.genre.pop", "secondaryGenreId": "music.genre.dance", "copyrightName": "Collaboration Records", "copyrightYear": 2024, "phonogramCopyright": "Collaboration Records", "phonogramCopyrightYear": 2024, "audioFormats": ["mp3", "wav"], "releaseOptions": ["digital", "streaming"], "mediaUrls": ["https://example.com/audio/track050.mp3"]}]}